import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Alert,
  Vibration,
  Image,
  I18nManager,
} from "react-native";
import {
  Trophy,
  TrendingUp,
  Award,
  LogOut,
  Clock,
  Heart,
  Scale,
  ArrowDownRight,
  ArrowUpRight,
} from "lucide-react-native";
import { createStyles } from "../style/UserScreen.style";

import { UserMealPlan } from "../components/users/UserMealPlan.native";
import { UserWorkoutPlan } from "../components/users/UserWorkoutPlan";
import { WorkoutLogger } from "../components/workout/WorkoutLogger.native";
import { RestTimer } from "../components/workout/RestTimer.native";
import { TabBar } from "../components/tabs/TabBar.native";
import { useAuthStore } from "../contexts/AuthContext";

import { getOverviewById, UserOverview } from "../data/overview";
import { WorkoutProvider } from "../contexts/WorkoutContext";
import { useTheme } from "../contexts/ThemeContext";
import { Sun, Moon } from "lucide-react-native";
import { useTranslation } from "react-i18next";

interface UserScreenProps {
  userId: string;
  onLogout: () => void;
}

const UserScreen: React.FC<UserScreenProps> = ({ userId, onLogout }) => {
  const { t } = useTranslation();

  const [activeTab, setActiveTab] = useState("overview");
  const [isWorkoutInProgress, setIsWorkoutInProgress] = useState(false);
  const [restTimer, setRestTimer] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [autoFinish, setAutoFinish] = useState(false);
  const { theme, setTheme, isDark } = useTheme();
  const styles = createStyles(isDark ? "dark" : "light");

  const { user } = useAuthStore();

  const userOverview: UserOverview = user
    ? {
        ...getOverviewById(user.id), 
        userName: user.name,
      }
    : getOverviewById("default");

  useEffect(() => {
    let interval: NodeJS.Timeout;
    // Add autoFinish check here
    if (restTimer > 0 && !isPaused && !autoFinish) {
      interval = setInterval(() => setRestTimer((prev) => prev - 1), 1000);
    }
    return () => clearInterval(interval);
  }, [restTimer, isPaused, autoFinish]);

  useEffect(() => {
    if (
      (restTimer === 0 || autoFinish) &&
      isWorkoutInProgress &&
      activeTab !== "workout-logger"
    ) {
      if (autoFinish) {
        Vibration.vibrate([400, 200, 400]);
        Alert.alert(
          t("workoutLogger.sessionEnd.title"),
          t("workoutLogger.sessionEnd.message"),
          [
            {
              text: t("workoutLogger.continue"),
              onPress: () => setActiveTab("workout-logger"),
            },
          ]
        );
      } else {
        Vibration.vibrate([400, 200, 400]);
        Alert.alert(
          t("workoutLogger.restTimeCompleted.title"),
          t("workoutLogger.restTimeCompleted.message"),
          [
            {
              text: t("workoutLogger.continue"),
              onPress: () => setActiveTab("workout-logger"),
            },
          ]
        );
      }
    }
  }, [restTimer, isWorkoutInProgress, activeTab]);

  const handleWorkoutStart = () => {
    setIsWorkoutInProgress(true);
    setAutoFinish(false);
  };

  const handleWorkoutEnd = () => {
    setIsWorkoutInProgress(false);
    setRestTimer(0);
    setIsPaused(false);
    setAutoFinish(false);
  };
  const handleRestStart = (duration: number) => setRestTimer(duration);
  const handleRestSkip = () => setRestTimer(0);
  const handlePauseToggle = (paused: boolean) => setIsPaused(paused);
  // const handleRestComplete = () => Vibration.vibrate(500);

  // const toggleMenu = () => setShowMenu(!showMenu);

  const handleTabPress = (tabId: string) => {
    setActiveTab(tabId);
    setShowMenu(false);
  };

  // Calculate performance metrics for display
  const calculateUserPerformance = () => {
    return Math.round(
      (userOverview.engagement?.score || 0) + (userOverview.progress?.rate || 0)
    );
  };

  // Get user initial for avatar placeholder
  const userInitial = userOverview.userName?.charAt(0).toUpperCase();

  // Calculate membership time left (dummy function)
  const getTimeLeft = () => {
    return "60";
  };

  // Render the overview tab content
  const renderOverviewTab = () => {
    // Calculate weight change (dummy data for now)
    const weightChange =
      (userOverview.currentWeight || 0) - (userOverview.startingWeight || 0);
    // Calculate fat change (dummy data for now)
    const fatChange =
      (userOverview.currentFatPercentage || 0) -
      (userOverview.startingFatPercentage || 0);

    return (
      <>
        {/* Main Profile Card */}
        <View style={styles.profileCard}>
          {/* Profile Header */}
          <View style={styles.profileHeader}>
            <View style={styles.avatarSection}>
              {userOverview.avatarUrl ? (
                <View style={styles.avatarContainer}>
                  <Image
                    source={{ uri: userOverview.avatarUrl }}
                    style={styles.avatar}
                  />
                </View>
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <Text style={styles.avatarText}>{userInitial}</Text>
                </View>
              )}
              <View style={styles.userNameContainer}>
                <Text style={styles.userName}>{userOverview.userName}</Text>
                <View style={styles.membershipBadge}>
                  <Clock
                    width={12}
                    height={12}
                    color={isDark ? "#C4B5FD" : "#059669"}
                  />
                  <Text style={styles.membershipText}>
                    {t("userScreen.membership.timeLeft").replace(
                      "{{days}}",
                      getTimeLeft()
                    )}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Simplified Metrics Grid */}
          <View style={styles.metricsGrid}>
            {/* Score Card - Now on the left */}
            <View style={styles.metricCard}>
              <View style={styles.metricIconContainer}>
                <Award
                  width={24}
                  height={24}
                  color={isDark ? "rgba(216, 180, 254, 1)" : "#4A90E2"}
                />
              </View>
              <Text style={styles.metricTitle}>
                {" "}
                {t("userScreen.metrics.score")}
              </Text>
              <Text style={styles.metricValue}>
                {calculateUserPerformance()}
              </Text>
              <View style={styles.changeIndicator}>
                {calculateUserPerformance() >= 85 ? (
                  <View style={styles.excellentChange}>
                    <Text style={styles.excellentChangeText}>
                      {t("userScreen.metrics.excellent")}
                    </Text>
                  </View>
                ) : calculateUserPerformance() >= 70 ? (
                  <View style={styles.goodChange}>
                    <Text style={styles.goodChangeText}>
                      {t("userScreen.metrics.good")}
                    </Text>
                  </View>
                ) : (
                  <View style={styles.onTrackChange}>
                    <Text style={styles.onTrackChangeText}>
                      {t("userScreen.metrics.onTrack")}
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* Body Fat Card - In the middle */}
            <View style={styles.metricCard}>
              <View style={styles.metricIconContainer}>
                <Heart
                  width={24}
                  height={24}
                  color={isDark ? "#C4B5FD" : "#4A90E2"}
                />
              </View>
              <Text style={styles.metricTitle}>
                {" "}
                {t("userScreen.metrics.bodyFat")}
              </Text>

              <Text style={styles.metricValue}>
                {userOverview.currentFatPercentage || 22}
                <Text style={styles.metricUnit}>%</Text>
              </Text>

              <View style={styles.changeIndicator}>
                {fatChange < 0 ? (
                  <View style={styles.positiveChange}>
                    <ArrowDownRight
                      width={12}
                      height={12}
                      color={isDark ? "rgba(254, 202, 202, 1)" : "#047857"}
                    />
                    <Text style={styles.positiveChangeText}>
                      {t("userScreen.metrics.decreased")} {Math.abs(fatChange)}%
                    </Text>
                  </View>
                ) : fatChange > 0 ? (
                  <View style={styles.negativeChange}>
                    <TrendingUp
                      width={12}
                      height={12}
                      color={isDark ? "#F87171" : "#B91C1C"}
                    />
                    <Text style={styles.negativeChangeText}>
                      {t("userScreen.metrics.increased")}
                      {fatChange}%
                    </Text>
                  </View>
                ) : (
                  <View style={styles.neutralChange}>
                    <Text style={styles.neutralChangeText}>ללא שינוי</Text>
                  </View>
                )}
              </View>
            </View>

            {/* Weight Card - Now on the right */}
            <View style={styles.metricCard}>
              <View style={styles.metricIconContainer}>
                <Scale
                  width={24}
                  height={24}
                  color={isDark ? "#C4B5FD" : "#4A90E2"}
                />
              </View>
              <Text style={styles.metricTitle}>
                {" "}
                {t("userScreen.metrics.weight")}
              </Text>
              <Text style={styles.metricValue}>
                {userOverview.currentWeight || 80}{" "}
                <Text style={styles.metricUnit}>
                  {t("userScreen.metrics.kg")}
                </Text>
              </Text>
              <View style={styles.changeIndicator}>
                {weightChange < 0 ? (
                  <View style={styles.positiveChange}>
                    <ArrowUpRight
                      width={12}
                      height={12}
                      color={isDark ? "rgba(254, 202, 202, 1)" : "#047857"}
                    />
                    <Text style={styles.positiveChangeText}>
                      {t("userScreen.metrics.increased")}
                      {Math.abs(weightChange)}
                    </Text>
                  </View>
                ) : weightChange > 0 ? (
                  <View style={styles.negativeChange}>
                    <TrendingUp
                      width={12}
                      height={12}
                      color={isDark ? "#F87171" : "#B91C1C"}
                    />
                    <Text style={styles.negativeChangeText}>
                      {t("userScreen.metrics.decreased")}
                      {weightChange}
                    </Text>
                  </View>
                ) : (
                  <View style={styles.neutralChange}>
                    <Text style={styles.neutralChangeText}>ללא שינוי</Text>
                  </View>
                )}
              </View>
            </View>
          </View>

          {/* Updates Section */}
          <View style={styles.updatesSection}>
            <Text
              style={[
                styles.sectionTitle,
                { textAlign: I18nManager.isRTL ? "right" : "left" },
              ]}
            >
              {t("userScreen.updates.title")}
            </Text>

            <View style={styles.updateCards}>
              <View style={styles.updateCard}>
                <View
                  style={[
                    styles.updateHeader,
                    {
                      flexDirection: I18nManager.isRTL ? "row-reverse" : "row",
                    },
                  ]}
                >
                  <Trophy
                    width={16}
                    height={16}
                    color={isDark ? "rgba(192, 132, 252, 1)" : "#4A90E2"}
                  />
                  <Text style={[styles.updateTitle]}>
                    {t("userScreen.updates.latestAchievement")}
                  </Text>
                </View>

                <Text
                  style={[
                    styles.updateText,
                    { textAlign: I18nManager.isRTL ? "right" : "left" },
                  ]}
                >
                  {userOverview.latestChanges ||
                    t("userScreen.updates.workoutAdjusted")}
                </Text>
              </View>

              <View style={styles.updateCard}>
                <View
                  style={[
                    styles.updateHeader,
                    {
                      flexDirection: I18nManager.isRTL ? "row-reverse" : "row",
                    },
                  ]}
                >
                  <Clock
                    width={16}
                    height={16}
                    color={isDark ? "rgba(192, 132, 252, 1)" : "#4A90E2"}
                  />
                  <Text style={[styles.updateTitle]}>
                    {t("userScreen.updates.latestActivity")}
                  </Text>
                </View>
                <Text
                  style={[
                    styles.updateText,
                    { textAlign: I18nManager.isRTL ? "right" : "left" },
                  ]}
                >
                  {userOverview.latestEvents ||
                    t("userScreen.updates.completedAssessment")}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </>
    );
  };

  // Render tab content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case "overview":
        return renderOverviewTab();
      case "meal-plan":
        return <UserMealPlan userId={userId} />;
      case "workout-plan":
        return <UserWorkoutPlan userId={userId} />;
      case "workout-logger":
        return (
          <WorkoutLogger
            userId={userId}
            onWorkoutStart={handleWorkoutStart}
            onWorkoutEnd={handleWorkoutEnd}
            onRestStart={handleRestStart}
            onPauseToggle={handlePauseToggle}
            restTimer={restTimer}
            isPaused={isPaused}
            autoFinish={autoFinish}
            setAutoFinish={setAutoFinish}
          />
        );
      default:
        return null;
    }
  };

  return (
    <WorkoutProvider>
      <SafeAreaView style={styles.container}>
        {/* Header with App Title, Menu and Logout */}
        <View style={{ padding: 12, paddingTop: 18, paddingBottom: 4 }}>
          <View style={styles.headerContainer}>
            <View style={styles.headerLeft}>
              <Text style={styles.appTitle}>{t("App.name")}</Text>
            </View>
            <View style={styles.headerRight}>
              <TouchableOpacity
                style={styles.menuButton}
                onPress={async () => {
                  if (theme === "system") {
                    await setTheme(isDark ? "dark" : "light");
                  } else {
                    // Toggle between light and dark
                    await setTheme(theme === "light" ? "dark" : "light");
                  }
                }}
              >
                {isDark ? (
                  <Sun
                    width={24}
                    height={24}
                    color={isDark ? "#FFFFFF" : "#3B82F6"}
                  />
                ) : (
                  <Moon width={24} height={24} color="#3B82F6" />
                )}
              </TouchableOpacity>
              <TouchableOpacity style={styles.logoutButton} onPress={onLogout}>
                <LogOut width={16} height={16} color="#FFFFFF" />
                <Text style={styles.logoutButtonText}>
                  {t("userScreen.actions.logout")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Tab Navigation */}
        <TabBar activeTab={activeTab} onTabChange={handleTabPress} />

        {/* Tab Content */}
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          {renderTabContent()}
        </ScrollView>

        {/* Floating Timer (shown when workout is in progress) */}
        {!autoFinish &&
          isWorkoutInProgress &&
          restTimer > 0 &&
          activeTab !== "workout-logger" && (
            <View style={styles.floatingTimerContainer}>
              <RestTimer
                time={restTimer}
                onSkip={handleRestSkip}
                toggleTimerPause={handlePauseToggle}
                isPaused={isPaused}
              />
            </View>
          )}
      </SafeAreaView>
    </WorkoutProvider>
  );
};

export default UserScreen;
